<script setup lang="ts">
import { ref, onMounted, nextTick, watch, computed } from 'vue';
import { Head } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import { useStreamingChat } from '@/composables/useStreamingChat';
import VueMarkdown from 'vue-markdown-render';
import type { BreadcrumbItemType } from '@/types';

// Breadcrumbs for navigation
const breadcrumbs: BreadcrumbItemType[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Prism Chat', href: '/chat' },
];

// Chat functionality
const {
    messages,
    input,
    isLoading,
    status,
    error,
    userContext,
    hasMessages,
    isStreaming,
    sendMessage,
    clearMessages,
    loadUserContext,
    clearError,
    stop,
} = useStreamingChat();

// UI state
const messagesContainer = ref<HTMLElement>();
const showWelcome = ref( true );

// Transform messages for Nuxt UI Pro format
const chatMessages = computed( () =>
{
    return messages.value.map( message => ( {
        id: message.id,
        content: message.content,
        role: message.role,
        createdAt: new Date( message.createdAt || Date.now() ),
        avatar: message.role === 'user'
            ? { src: '/api/user/avatar', alt: 'User' }
            : { src: '/images/ai-avatar.png', alt: 'AI Assistant' }
    } ) );
} );

// Auto-scroll to bottom when new messages arrive
const scrollToBottom = async () =>
{
    await nextTick();
    if ( messagesContainer.value )
    {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
};

// Watch for new messages and scroll
watch( messages, scrollToBottom, { deep: true } );
watch( isLoading, ( loading ) =>
{
    if ( loading )
    {
        scrollToBottom();
    }
} );

// Handle stopping generation
const handleStop = () =>
{
    stop();
};

// Handle clearing chat
const handleClearChat = () =>
{
    clearMessages();
    showWelcome.value = true;
    clearError();
};

// Handle error actions
const handleErrorDismiss = () =>
{
    clearError();
};

const handleErrorRetry = async () =>
{
    clearError();
    if ( input.value.trim() )
    {
        await sendMessage( input.value );
    }
};

// Handle form submission (UChatPrompt handles Enter key automatically)
const handleSubmit = ( event?: Event ) =>
{
    if ( event )
    {
        event.preventDefault();
    }

    if ( !input.value.trim() || isLoading.value ) return;

    showWelcome.value = false;

    if ( !input.value.trim() || error.value || input.value.length > 4000 )
    {
        return;
    }

    sendMessage( input.value.trim() );
};

// Load user context on mount
onMounted( async () =>
{
    await loadUserContext();
} );

// Welcome message examples
const examplePrompts = [
    "Help me create a prompt for content writing",
    "How can I improve this prompt for better results?",
    "What are the best practices for prompt engineering?",
    "Create a template for analyzing customer feedback",
];

const handleExampleClick = ( prompt: string ) =>
{
    input.value = prompt;
    handleSubmit();
};
</script>

<template>

    <Head title="Prism Chat" />

    <AppLayout :breadcrumbs=" breadcrumbs ">
        <div class="flex h-[calc(100vh-8rem)] flex-col p-4">
            <!-- Chat Header -->
            <div class="flex items-center justify-between border-b bg-white dark:bg-gray-900 px-6 py-4">
                <div class="flex items-center gap-3">
                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                        <svg class="h-6 w-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900 dark:text-white">Prism Prompt Engineer</h1>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            AI-powered prompt engineering assistant
                        </p>
                    </div>
                </div>

                <div class="flex items-center gap-2">
                    <!-- Clear chat button -->
                    <UButton v-if=" hasMessages " @click=" handleClearChat " icon="i-lucide-trash-2" variant="outline"
                        color="gray" size="sm">
                        Clear Chat
                    </UButton>
                </div>
            </div>

            <!-- Messages area -->
            <div ref="messagesContainer" class="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900/50">
                <!-- Welcome screen -->
                <div v-if=" showWelcome && !hasMessages " class="flex h-full items-center justify-center p-8">
                    <UCard class="w-full max-w-2xl">
                        <div class="text-center">
                            <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                                <UIcon name="i-lucide-message-circle" class="h-8 w-8 text-primary" />
                            </div>
                            <h2 class="mt-4 text-2xl font-semibold text-gray-900 dark:text-white">
                                Welcome to Prism Chat
                            </h2>
                            <p class="mt-2 text-gray-600 dark:text-gray-400">
                                Your AI-powered prompt engineering assistant. Ask me anything about creating,
                                optimizing, and analyzing prompts for better AI interactions.
                            </p>
                        </div>

                        <div class="mt-8 space-y-4">
                            <h3 class="font-medium text-gray-900 dark:text-white">Try these examples:</h3>
                            <div class="grid gap-2">
                                <UButton v-for=" prompt in examplePrompts " :key=" prompt "
                                    @click="handleExampleClick( prompt )" variant="outline" color="gray" size="sm" :ui=" {
                                        base: 'justify-start text-left',
                                        padding: { sm: 'px-3 py-3' }
                                    } " class="h-auto whitespace-normal">
                                    {{ prompt }}
                                </UButton>
                            </div>
                        </div>
                    </UCard>
                </div>
            
                <!-- Chat messages using UChatMessages -->
                <UChatMessages v-else :messages=" chatMessages " :status="'submitted'" 
                
                :user=" {
                    side: 'right',
                    variant: 'outline',
                    avatar: { icon: 'i-lucide-user' }
                } " auto-scroll-icon="i-lucide-arrow-down" 
                
                :assistant=" {
                    side: 'left',
                    variant: 'outline',
                    avatar: { icon: 'i-lucide-bot' }
                } " class="px-4">
                    <template #content=" { message } ">
                        <VueMarkdown :source=" message.content " />
                        <div class="absolute" v-if=" status === 'ready' && message.role === 'assistant' ">
                            <UButton @click=" () => { } " icon="i-lucide-clipboard" variant="ghost" size="sm" />
                        </div>
                    </template>
                </UChatMessages>
            </div>

            <!-- Error display -->
            <div v-if=" error " class="border-t p-4">
                <div class="mx-auto max-w-4xl">
                    <UAlert :title=" error.type === 'validation' ? 'Invalid Input' : 'Error' "
                        :description=" error.message " icon="i-lucide-alert-circle" color="red" variant="soft"
                        :close-button=" { icon: 'i-lucide-x', color: 'gray', variant: 'link', onClick: handleErrorDismiss } " />
                </div>
            </div>

            <!-- Input area using UChatPrompt -->
            <div class="border-t bg-white p-4 dark:bg-gray-900">
                <div class="mx-auto max-w-4xl">
                    <UChatPrompt v-model=" input " placeholder="Ask me anything about prompt engineering..."
                        :error=" error " :maxlength=" 4000 " :maxrows=" 5 " variant="outline" @submit=" handleSubmit ">
                        <template #header>
                            <!-- Character count -->
                            <div v-if=" input.length > 0 " class="text-xs text-gray-500" :class=" {
                                'text-amber-600': input.length > 3200,
                                'text-red-600': input.length > 4000,
                            } ">
                                {{ input.length }}/4000
                            </div>
                        </template>

                        <template #footer>
                            <div class="text-xs text-gray-500">
                                Press Enter to send, Shift+Enter for new line
                            </div>

                            <UChatPromptSubmit :status=" isLoading ? 'streaming' : 'ready' " @stop=" handleStop " />
                        </template>
                    </UChatPrompt>
                </div>
            </div>
        </div>
    </AppLayout>
</template>