<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Styling Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Markdown/Prose Styling for Chat Messages */
        .prose-chat {
            color: rgb(15 23 42);
            line-height: 1.625;
        }
        
        .prose-chat h1,
        .prose-chat h2,
        .prose-chat h3,
        .prose-chat h4,
        .prose-chat h5,
        .prose-chat h6 {
            font-weight: 600;
            color: rgb(15 23 42);
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }
        
        .prose-chat h1:first-child,
        .prose-chat h2:first-child,
        .prose-chat h3:first-child,
        .prose-chat h4:first-child,
        .prose-chat h5:first-child,
        .prose-chat h6:first-child {
            margin-top: 0;
        }
        
        .prose-chat h1 {
            font-size: 1.5rem;
        }
        
        .prose-chat h2 {
            font-size: 1.25rem;
        }
        
        .prose-chat h3 {
            font-size: 1.125rem;
        }
        
        .prose-chat h4,
        .prose-chat h5,
        .prose-chat h6 {
            font-size: 1rem;
        }
        
        .prose-chat p {
            margin-bottom: 1rem;
        }
        
        .prose-chat p:last-child {
            margin-bottom: 0;
        }
        
        .prose-chat ul,
        .prose-chat ol {
            margin-bottom: 1rem;
            padding-left: 1.5rem;
        }
        
        .prose-chat ul > li {
            list-style-type: disc;
            margin-bottom: 0.25rem;
        }
        
        .prose-chat ol > li {
            list-style-type: decimal;
            margin-bottom: 0.25rem;
        }
        
        .prose-chat blockquote {
            border-left: 4px solid rgb(229 231 235);
            padding-left: 1rem;
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
            margin: 1rem 0;
            background-color: rgb(249 250 251);
            border-radius: 0 0.375rem 0.375rem 0;
            font-style: italic;
            color: rgb(107 114 128);
        }
        
        .prose-chat code {
            background-color: rgb(243 244 246);
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
            color: rgb(15 23 42);
            border: 1px solid rgb(229 231 235);
        }
        
        .prose-chat pre {
            background-color: rgb(243 244 246);
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1rem 0;
            border: 1px solid rgb(229 231 235);
        }
        
        .prose-chat pre code {
            background-color: transparent;
            padding: 0;
            font-size: 0.875rem;
            border: none;
        }
        
        .prose-chat strong {
            font-weight: 600;
            color: rgb(15 23 42);
        }
        
        .prose-chat em {
            font-style: italic;
        }
        
        .prose-chat a {
            color: rgb(59 130 246);
            text-decoration: underline;
        }
        
        .prose-chat a:hover {
            color: rgb(37 99 235);
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Markdown Styling Test</h1>
        
        <div class="bg-white p-6 rounded-lg shadow-sm border">
            <div class="prose-chat">
                <h1>Main Heading</h1>
                <p>This is a paragraph with <strong>bold text</strong> and <em>italic text</em>. Here's some <code>inline code</code> and a <a href="#">link</a>.</p>
                
                <h2>Secondary Heading</h2>
                <p>Here's a list:</p>
                <ul>
                    <li>First item</li>
                    <li>Second item with <code>code</code></li>
                    <li>Third item</li>
                </ul>
                
                <h3>Code Block</h3>
                <pre><code>function example() {
    console.log("Hello, world!");
    return true;
}</code></pre>
                
                <blockquote>
                    <p>This is a blockquote with some important information.</p>
                </blockquote>
                
                <p>Final paragraph to test spacing.</p>
            </div>
        </div>
    </div>
</body>
</html>
